package com.aliyun.dba.poddefault.action.service;

import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.model.InstanceLevel;
import com.aliyun.apsaradb.dbaasmetaapi.model.ReplicaSet;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.custins.dataobject.CustinsParamDO;
import com.aliyun.dba.custins.dataobject.MinorVersionReleaseDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.MinorVersionService;
import com.aliyun.dba.custins.support.CustinsParamSupport;
import com.aliyun.dba.custins.support.sysparamgroup.SysParamGroupHelper;
import com.aliyun.dba.poddefault.action.service.createReadOnly.request.CreateReadOnlyInsRequest;
import com.aliyun.dba.poddefault.action.support.PodCommonSupport;
import com.aliyun.dba.support.property.ErrorCode;
import com.aliyun.dba.support.property.RdsException;
import com.aliyun.dba.support.property.ResultCode;
import com.aliyun.rds.logsdk.logagent.define.LogAgent;
import com.aliyun.rds.logsdk.logagent.factory.LogFactory;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.aliyun.dba.custins.support.CustinsParamSupport.getParameterValue;
import static com.aliyun.dba.poddefault.action.support.PodDefaultConstants.MYSQL_VERSION_80;
import static com.aliyun.dba.support.common.registry.dataobject.KindCodeParser.KIND_CODE_NEW_ARCH;
import static com.aliyun.dba.support.property.ErrorCode.INVALID_MINOR_VERSION_NOT_FOUND;

/**
 * @author: 光齐
 * @create-date: 2025/6/9 16:00
 */
@Service
public class DuckDBService {
    private static final LogAgent logger = LogFactory.getLogAgent(DuckDBService.class);

    @Autowired
    private CustinsParamService custinsParamService;

    @Resource
    private DBaasMetaService dBaasMetaService;

    @Resource
    private MinorVersionServiceHelper minorVersionServiceHelper;

    /**
     * 判断是否为分析型只读实例
     *
     * @param params 请求参数
     * @return true 如果是分析型只读实例，false 否则
     */
    public boolean isAnalyticReadOnlyIns(Map<String, String> params) {
        String isAnalyticReadOnlyIns = getParameterValue(params, "isAnalyticReadOnlyIns");
        return StringUtils.isNotBlank(isAnalyticReadOnlyIns) && Boolean.parseBoolean(isAnalyticReadOnlyIns);
    }

    public String getAnalyticReadOnlyInsParamGroupId(CreateReadOnlyInsRequest request, ReplicaSet readReplicaSet) throws ApiException, RdsException {
        ReplicaSet primaryReplicaSet = dBaasMetaService.getDefaultClient().getReplicaSet(request.getRequestId(), request.getDbInstanceName(),false);
        CustinsParamDO primaryParamGroupIdDO = custinsParamService.getCustinsParam(primaryReplicaSet.getId().intValue(), CustinsParamSupport.CUSTINS_PARAM_NAME_PARAM_GROUP_ID);
        String paramGroupId;
        Map<String, String> map;
        if (primaryParamGroupIdDO == null || !primaryParamGroupIdDO.getValue().startsWith("rpg-sys-")) {
            // 如果是用户参数模板 使用官方 duckDB 模板
            map = new HashMap<>();
            map.put("db_type", request.getDbType());
            map.put("db_version", request.getDbVersion());
            map.put("character_type", "normal");
            map.put("category", Objects.requireNonNull(readReplicaSet.getCategory()).toLowerCase());
            map.put("db_storage_engine", "duckdb");
            map.put("performance_mode", "safe");
            map.put("kind_code", "any");
        } else {
            //todo:先只支持safe模版
            Map<String, Object> paramGroupInfo = SysParamGroupHelper.describeSysParamGroupId(primaryParamGroupIdDO.getValue());
            paramGroupInfo.put("db_storage_engine", "duckdb");
            paramGroupInfo.put("performance_mode", "safe");
            map = paramGroupInfo.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> (String) e.getValue()));
        }
        paramGroupId = SysParamGroupHelper.getSysParamGroupId(map);
        logger.info("paramGroupId is: {}, request map is {}", paramGroupId, map.toString());
        return paramGroupId;
    }

    public void checkConditionsForCreateAnalyticsReadOnlyIns(String requestId, String dbVersion, InstanceLevel instanceLevel, boolean isReadIns) throws RdsException {
        if (!isReadIns) {
            throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "UnsupportedInstanceLevel", "Only support read instance"});
        }

        if (!MYSQL_VERSION_80.equals(dbVersion)) {
            logger.error("{} ins db version {}, and Buffer Pool Extension only support 8.0 .", requestId, dbVersion);
            throw new RdsException(ErrorCode.INVALID_ENGINEVERSION);
        }

        if (PodCommonSupport.isServerless(instanceLevel)) {
            // serverless暂时不支持分析型只读实例
            throw new RdsException(new Object[]{ResultCode.CODE_UNSUPPORTED, "UnsupportedInstanceLevel", "serverless can't support analytics read instance"});
        }
    }

    public String getAnalyticReadOnlyInsLastMinorVersion(String requestId, String dbInstanceName) throws ApiException, RdsException {
        List<ReplicaSet> readOnlyReplicaSetList = ObjectUtils.firstNonNull(dBaasMetaService.getDefaultClient().listReplicaSetSubIns(requestId, dbInstanceName, ReplicaSet.InsTypeEnum.READONLY.toString()).getItems(), new ArrayList<>());
        boolean isAnalyticReadOnlyIns = false;
        for (val readonlyReplicaSet : readOnlyReplicaSetList) {

            Map<String, String> labels = dBaasMetaService.getDefaultClient().listReplicaSetLabels(requestId, readonlyReplicaSet.getName());

            // 分析型只读实例内核版本与主实例解藕
            String isAnalyticReadOnlyInsValue = labels.get("isAnalyticReadOnlyIns");
            if (StringUtils.isNotBlank(isAnalyticReadOnlyInsValue) && ("true".equalsIgnoreCase(isAnalyticReadOnlyInsValue))) {
                isAnalyticReadOnlyIns = true;
                String releaseDate = minorVersionServiceHelper.fetchReleaseDateBySpecTag(readonlyReplicaSet.getService(),
                                                                                        readonlyReplicaSet.getServiceVersion(),
                                                                                        KIND_CODE_NEW_ARCH,
                                                                                        readonlyReplicaSet.getCategory(),
                                                                                        MinorVersionServiceHelper.ServiceTag.TAG_ALISQL_DUCKDB_DOCKER_IMAGE.getTagPrefix());
                if (CollectionUtils.isEmpty(minorVersionReleaseDOS)) {
                    return null;
                }
                break;
            }
        }

    }

}
