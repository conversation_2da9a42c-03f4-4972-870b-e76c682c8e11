package com.aliyun.dba.poddefault.action.service;

import com.aliyun.apsaradb.activityprovider.model.ScheduleTemplate;
import com.aliyun.apsaradb.dbaasmetaapi.ApiException;
import com.aliyun.apsaradb.dbaasmetaapi.api.DefaultApi;
import com.aliyun.apsaradb.dbaasmetaapi.model.*;
import com.aliyun.dba.bak.dataobject.BakhistoryDO;
import com.aliyun.dba.bak.service.BakService;
import com.aliyun.dba.base.cache.LocalCacheService;
import com.aliyun.dba.base.lib.BackupService;
import com.aliyun.dba.base.lib.CommonProviderService;
import com.aliyun.dba.base.lib.DBaasMetaService;
import com.aliyun.dba.base.lib.WorkFlowService;
import com.aliyun.dba.base.parameter.MysqlParameterHelper;
import com.aliyun.dba.base.response.backup.GetBackupSetResponse;
import com.aliyun.dba.base.response.backup.OssBakRestoreResponse;
import com.aliyun.dba.base.service.MysqlParamSupport;
import com.aliyun.dba.base.service.ReplicaSetService;
import com.aliyun.dba.base.service.SlrCheckService;
import com.aliyun.dba.base.service.WhitelistTemplateService;
import com.aliyun.dba.common.dataobject.AvailableZoneInfoDO;
import com.aliyun.dba.common.dataobject.MultiAVZExParamDO;
import com.aliyun.dba.custins.dataobject.CustInstanceDO;
import com.aliyun.dba.custins.service.CustinsParamService;
import com.aliyun.dba.custins.service.CustinsService;
import com.aliyun.dba.custins.support.AVZInfo;
import com.aliyun.dba.custins.support.AVZSupport;
import com.aliyun.dba.poddefault.action.support.*;
import com.aliyun.dba.resource.service.ResourceService;
import com.aliyun.dba.service.MySQLAvzService;
import com.aliyun.dba.service.MySQLServiceImpl;
import com.aliyun.dba.support.common.registry.dataobject.KindCodeParser;
import com.aliyun.dba.support.property.ParamConstants;
import com.aliyun.dba.support.property.RdsException;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.aliyun.dba.poddefault.action.support.PodCreateInsParam.checkValidDispenseMode;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import org.mockito.InjectMocks;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.aliyun.dba.base.support.HashUtils;

@RunWith(MockitoJUnitRunner.class)
public class AliyunCreateDBInstanceServiceTest {
    @Mock
    private ReplicaSetService replicaSetService;

    private HashMap<String, String> params;
    @Mock
    private AliyunInstanceDependency dependency;

    @Mock
    private MysqlParamSupport mysqlParamSupport;

    @Mock
    private DBaasMetaService dBaasMetaService;

    @Mock
    private DefaultApi defaultApi;

    @Mock
    private MinorVersionServiceHelper minorVersionServiceHelper;

    @Mock
    private PodCommonSupport podCommonSupport;
    @Mock
    private CloudDiskCompressionHelper cloudDiskCompressionHelper;
    @Mock
    private ResourceService resourceService;
    @Mock
    private PodParameterHelper podParameterHelper;
    @Mock
    private PodAvzSupport avzSupport;
    @Mock
    private RundPodSupport rundPodSupport;
    @Mock
    private AligroupService aligroupService;
    @Mock
    private MySQLServiceImpl mySQLservice;
    @Mock
    private PodTemplateHelper podTemplateHelper;
    @Mock
    private PodReplicaSetResourceHelper podReplicaSetResourceHelper;
    @Mock
    private CommonProviderService commonProviderService;
    @Mock
    private com.aliyun.apsaradb.activityprovider.api.DefaultApi commonDefaultApi;
    @Mock
    private WhitelistTemplateService whitelistTemplateService;
    @Mock
    private CustinsParamService custinsParamService;
    @Mock
    private WorkFlowService workFlowService;
    @Mock
    private BackupService backupService;
    @Mock
    private BakService bakService;
    @Mock
    private CustinsService custinsService;
    @Mock
    private MysqlParameterHelper mysqlParaHelper;
    @Mock
    private ColdDataService coldDataService;
    @Mock
    private SlrCheckService slrCheckService;


    @InjectMocks
    private AliyunCreateDBInstanceService aliyunCreateDBInstanceService;

    private Map<String, Boolean> initParams;

    @Test
    public void createDBInstance() {
        HashMap<String, String> params = new HashMap<String, String>();
        params.put("ResourceGroupId", "rg-58aiyyy78vgw86n");
        params.put("DBInstanceNetType", "1");
        params.put("DBInstanceType", "x");
        params.put("Timezone", "china");
        params.put("MultiAVZExParam", "%7B%22availableZoneInfoList%22%3A%5B%7B%22isUserSpecified%22%3Atrue%2C%22region%22%3A%22cn-zhangjiakou-a-aliyun%22%2C%22role%22%3A%22master%22%2C%22zoneID%22%3A%22cn-zhangjiakou-a%22%7D%5D%7D");
        params.put("StorageEngine", "innodb");
        params.put("SecurityIPList", "0.0.0.0%2F0");
        params.put("ClusterName", "test_mysql_dedicate_group_console");
        params.put("OpsServiceVersion", "2.0");
        params.put("Engine", "mysql");
        params.put("DBInstanceName", "k8smysqlbasic1602565479pk3");
        params.put("EngineVersion", "5.7");
        params.put("DBInstanceClass", "mysql.n1.micro.1");
        params.put("VPCId", "vpc-xxxmmxjqqi8go8153uq3b");
        params.put("DBInstanceStorageType", "cloud_ssd");
        params.put("VswitchId", "vsw-8vb5wxejg4owbtmkdepzo");
        params.put("Storage", "20");
        params.put("Region", "cn-zhangjiakou-a-aligroup");
        params.put("OssFileSize", "2097152");
        params.put("OssUrl", "https://mysql57-restore.oss-cn-zhangjiakou.aliyuncs.com/backupall_qp.xb?Expires=1606220745%26OSSAccessKeyId=TMP.****************************************************************************************3xZ13p%26Signature=dP%252BDGGD4FdZGQsGfHVVSSehRSF0%253D");
        params.put("OssBucket", "test");
        params.put("OssFilePath", "test");
        params.put("PreferredBackupTime".toLowerCase(), "18:28Z");
        params.put("preferredbackupperiod".toLowerCase(), "1010101");
        params.put("OssFileName", "backup_all_qp.xb");
        params.put("OssFileMetaData", "");
        AliyunCreateDBInstanceService aliyunCreateDBInstanceService = new AliyunCreateDBInstanceService();
        try {
            aliyunCreateDBInstanceService.createDBInstance(null,params);
        }catch (Exception e){
            Assert.assertTrue(e != null);
        }

        try {
            aliyunCreateDBInstanceService.createDBInstanceWithOss(params);
        }catch (Exception e){
            Assert.assertTrue(e != null);
        }
    }


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        params = new HashMap<>();
        params.put("ResourceGroupId", "rg-58aiyyy78vgw86n");
        params.put("DBInstanceNetType", "1");
        params.put("DBInstanceType", "x");
        params.put("Timezone", "china");
        params.put("MultiAVZExParam", "%7B%22availableZoneInfoList%22%3A%5B%7B%22isUserSpecified%22%3Atrue%2C%22region%22%3A%22cn-zhangjiakou-a-aliyun%22%2C%22role%22%3A%22master%22%2C%22zoneID%22%3A%22cn-zhangjiakou-a%22%7D%5D%7D");
        params.put("StorageEngine", "innodb");
        params.put("SecurityIPList", "0.0.0.0%2F0");
        params.put("ClusterName", "test_mysql_dedicate_group_console");
        params.put("OpsServiceVersion", "2.0");
        params.put("Engine", "mysql");
        params.put("DBInstanceName", "k8smysqlbasic1602565479pk3");
        params.put("EngineVersion", "5.7");
        params.put("DBInstanceClass", "mysql.n1.micro.1");
        params.put("VPCId", "vpc-xxxmmxjqqi8go8153uq3b");
        params.put("DBInstanceStorageType", "cloud_ssd");
        params.put("VswitchId", "vsw-8vb5wxejg4owbtmkdepzo");
        params.put("Storage", "20");
        params.put("Region", "cn-zhangjiakou-a-aligroup");
        params.put("OssFileSize", "2097152");
        params.put("OssUrl", "https://mysql57-restore.oss-cn-zhangjiakou.aliyuncs.com/backupall_qp.xb?Expires=1606220745%26OSSAccessKeyId=TMP.****************************************************************************************3xZ13p%26Signature=dP%252BDGGD4FdZGQsGfHVVSSehRSF0%253D");
        params.put("OssBucket", "test");
        params.put("OssFilePath", "test");
        params.put("PreferredBackupTime".toLowerCase(), "18:28Z");
        params.put("preferredbackupperiod".toLowerCase(), "1010101");
        params.put("OssFileName", "backup_all_qp.xb");
        params.put("OssFileMetaData", "");
        params.put("superAccountPassword", "123qwe!@#");
        params.put("superAccount", "account1");

        initParams = new HashMap<>();
        initParams.put("bizType", true);
        initParams.put("diskType", true);
        initParams.put("isSingleNode", true);
    }

    @Test
    public void account_ValidCredentials_AccountCreated() throws Exception {
        // Arrange
        String requestId = "test-request-id";
        String dbInstanceName = "test-instance-name";
        String superAccountName = "test-user";
        String superAccountPassword = "test-password";
        PodCreateInsParam metaParam = new PodCreateInsParam(dependency, params);
        metaParam.setDbInstanceName(dbInstanceName);
        metaParam.setSuperAccountName(superAccountName);
        metaParam.setSuperAccountPassword(superAccountPassword);
        metaParam.setDbType("mysql");
        metaParam.setDbVersion("5.7");
        DefaultApi mockClient = mock(DefaultApi.class);
        Account mockAccount = mock(Account.class);

        Field dBaasMetaService = aliyunCreateDBInstanceService.getClass().getDeclaredField("dBaasMetaService");
        dBaasMetaService.setAccessible(true);
        DBaasMetaService dBaasMetaServiceObject = mock(DBaasMetaService.class);
        dBaasMetaService.set(aliyunCreateDBInstanceService, dBaasMetaServiceObject);
        when(dBaasMetaServiceObject.getDefaultClient()).thenReturn(mockClient);

        when(mockClient.createAccountForReplicaSet(anyString(), anyString(), any(Account.class))).thenReturn(mockAccount); // 或者返回你期望的结果
        // Act

        try {
            aliyunCreateDBInstanceService.setSuperAccount(metaParam, requestId);
        }catch (Exception e){
            Assert.fail();
        }
    }


//    @Test
//    public void testInitReplicaSetLabels() throws Exception {
//        // Arrange
//        String requestId = "testRequestId";
//        PodCreateInsParam metaParam = new PodCreateInsParam(dependency, params);
//        metaParam.setParamGroupId("testParamGroupId");
//        metaParam.setBizType(ReplicaSet.BizTypeEnum.ALIYUN);
//        metaParam.setDiskType(Replica.StorageTypeEnum.CLOUD_ESSD.toString());
//        metaParam.setSingleNode(false);
//        metaParam.setInstructionSetArch("x86");
//        metaParam.setDbType("mysql");
//
//        String config = "MYSQL_HACK_FOR_ARM_TURN_ON_OPTIMIZED_WRITES_FOR_EXT";
//        ConfigListResult configListResult = new ConfigListResult();
//        List<Config> items = new ArrayList<>();
//        Config configItem = new Config();
//        configItem.setValue("true");
//        items.add(configItem);
//        configListResult.setItems(items);
//
//        ReplicaSet replicaSet = new ReplicaSet();
//        replicaSet.setName("testReplicaSetName");
//
//        DefaultApi mockClient = mock(DefaultApi.class);
//        Field dBaasMetaService = aliyunCreateDBInstanceService.getClass().getDeclaredField("dBaasMetaService");
//        dBaasMetaService.setAccessible(true);
//        DBaasMetaService dBaasMetaServiceObject = mock(DBaasMetaService.class);
//        dBaasMetaService.set(aliyunCreateDBInstanceService, dBaasMetaServiceObject);
////        when(dBaasMetaServiceObject.getDefaultClient()).thenReturn(mockClient);
////        when(mockClient.listConfigs(anyString(), anyString())).thenReturn(configListResult);
//
//        metaParam.setInitParams(initParams);
//
//        AliyunCreateDBInstanceService aliyunCreateDBInstanceServiceSpy = new AliyunCreateDBInstanceService();
//        Whitebox.setInternalState(aliyunCreateDBInstanceServiceSpy, "minorVersionServiceHelper", minorVersionServiceHelper);
//        Whitebox.setInternalState(aliyunCreateDBInstanceServiceSpy, "dBaasMetaService", dBaasMetaServiceObject);
//        Whitebox.invokeMethod(aliyunCreateDBInstanceServiceSpy, "initReplicaSetLabels", requestId, metaParam, replicaSet);
//
//        verify(mockClient, times(0)).listConfigs(anyString(), anyString());
//    }

    @Test
    public void testCreateDBInstance() throws Exception {
        when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        when(dependency.getDBaasMetaService()).thenReturn(dBaasMetaService);
        when(dependency.getResourceService()).thenReturn(resourceService);
        when(dependency.getPodParameterHelper()).thenReturn(podParameterHelper);
        when(dependency.getAvzSupport()).thenReturn(avzSupport);
        when(dependency.getRundPodSupport()).thenReturn(rundPodSupport);
        when(dependency.getCloudDiskCompressionHelper()).thenReturn(cloudDiskCompressionHelper);
        when(dependency.getReplicaSetService()).thenReturn(replicaSetService);
        when(dependency.getAligroupService()).thenReturn(aligroupService);
        when(dependency.getMySQLservice()).thenReturn(mySQLservice);
        when(dependency.getBackupService()).thenReturn(backupService);
        when(dependency.getBakService()).thenReturn(bakService);
        when(dependency.getCustinsService()).thenReturn(custinsService);
        GetBackupSetResponse backupSetResponse=new GetBackupSetResponse();
        backupSetResponse.setEngine("MySQL");
        backupSetResponse.setEngineVersion("5.7");
        backupSetResponse.setSlaveStatusObj(new GetBackupSetResponse.SlaveStatus());
        backupSetResponse.getSlaveStatusObj().setSnapshotId("snapshotId");
        when(backupService.getBackupSet(any())).thenReturn(backupSetResponse);
        BakhistoryDO bakhistoryDO=new BakhistoryDO();
        bakhistoryDO.setKindCode(18);
        when(bakService.getBakhistoryByBackupSetId(any(),any())).thenReturn(bakhistoryDO);
        AVZInfo avzInfo = new AVZInfo(ParamConstants.DispenseMode.ClassicDispenseMode, "regionId", "zoneId", "vswId", "regionId",null, new MultiAVZExParamDO());
        when(avzSupport.getAVZInfo(anyMap())).thenReturn(avzInfo);
        when(podParameterHelper.getAndCheckReplicaSetIpWhiteList()).thenReturn(new IPWhiteList());
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.BASIC);
        when(defaultApi.getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setId(1L);

        when(mysqlParamSupport.getParameterValue(any(),eq(ParamConstants.STORAGE),any())).thenReturn("100");
        when(mysqlParamSupport.getAndCheckDBType(any(), any())).thenReturn("MySQL");
        when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.SOURCE_DBINSTANCE_ID))).thenReturn("123");
        when(mysqlParamSupport.getAndCheckDBVersion(anyMap(),anyString(),anyBoolean())).thenReturn("5.7");
        when(podParameterHelper.getDiskType(any())).thenReturn("cloud_auto");
        when(podParameterHelper.getBizType(any(), any())).thenReturn(ReplicaSet.BizTypeEnum.ALIYUN);
        when(cloudDiskCompressionHelper.getCompressionMode(any(), any(), any())).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_ON);
        when(cloudDiskCompressionHelper.getCompressionRatio(any(),any(),any())).thenReturn(2.0);


        Map<String, String> createParams = new HashMap<>();
        createParams.put("vpcid", "vpc-xxxmmxjqqi8go8153uq3b");
        createParams.put("vswitchid", "vsw-8vb5wxejg4owbtmkdepzo");
        createParams.put("connectionstring", "rm-2ze6ls1s41a33926zxx");
        createParams.put("dbinstancestoragetype","cloud_auto");
        createParams.put("PreferredBackupTime".toLowerCase(), "06:56Z");
        createParams.put("preferredbackupperiod".toLowerCase(), "1010101");
        createParams.put("sourcedbinstanceid","123");
        createParams.put("backupsetid","1234");
        CustInstanceDO sourceCustInstanceDO = new CustInstanceDO();
        sourceCustInstanceDO.setKindCode(3);
        when(custinsService.getCustInstanceByCustinsIdIgnoreDelete(any(),any(),any())).thenReturn(sourceCustInstanceDO);
        Map<String, Object> data = aliyunCreateDBInstanceService.createDBInstance(null, createParams);
        Assert.assertNotNull(data);
    }

    @Test
    public void testCreateDBInstance_18() throws Exception {
        when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        when(dependency.getDBaasMetaService()).thenReturn(dBaasMetaService);
        when(dependency.getResourceService()).thenReturn(resourceService);
        when(dependency.getPodParameterHelper()).thenReturn(podParameterHelper);
        when(dependency.getAvzSupport()).thenReturn(avzSupport);
        when(dependency.getRundPodSupport()).thenReturn(rundPodSupport);
        when(dependency.getCloudDiskCompressionHelper()).thenReturn(cloudDiskCompressionHelper);
        when(dependency.getReplicaSetService()).thenReturn(replicaSetService);
        when(dependency.getAligroupService()).thenReturn(aligroupService);
        when(dependency.getMySQLservice()).thenReturn(mySQLservice);
        when(dependency.getBackupService()).thenReturn(backupService);
        when(dependency.getBakService()).thenReturn(bakService);
        when(dependency.getCustinsService()).thenReturn(custinsService);
        GetBackupSetResponse backupSetResponse=new GetBackupSetResponse();
        backupSetResponse.setEngine("MySQL");
        backupSetResponse.setEngineVersion("5.7");
        backupSetResponse.setSlaveStatusObj(new GetBackupSetResponse.SlaveStatus());
        backupSetResponse.getSlaveStatusObj().setSnapshotId("snapshotId");
        when(backupService.getBackupSet(any())).thenReturn(backupSetResponse);
        BakhistoryDO bakhistoryDO=new BakhistoryDO();
        bakhistoryDO.setKindCode(18);
        when(bakService.getBakhistoryByBackupSetId(any(),any())).thenReturn(bakhistoryDO);
        AVZInfo avzInfo = new AVZInfo(ParamConstants.DispenseMode.ClassicDispenseMode, "regionId", "zoneId", "vswId", "regionId",null, new MultiAVZExParamDO());
        when(avzSupport.getAVZInfo(anyMap())).thenReturn(avzInfo);
        when(podParameterHelper.getAndCheckReplicaSetIpWhiteList()).thenReturn(new IPWhiteList());
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.BASIC);
        when(defaultApi.getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setId(1L);

        when(mysqlParamSupport.getParameterValue(any(),eq(ParamConstants.STORAGE),any())).thenReturn("100");
        when(mysqlParamSupport.getAndCheckDBType(any(), any())).thenReturn("MySQL");
        when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.SOURCE_DBINSTANCE_ID))).thenReturn("123");
        when(mysqlParamSupport.getAndCheckDBVersion(anyMap(),anyString(),anyBoolean())).thenReturn("5.7");
        when(podParameterHelper.getDiskType(any())).thenReturn("cloud_auto");
        when(podParameterHelper.getBizType(any(), any())).thenReturn(ReplicaSet.BizTypeEnum.ALIYUN);

        when(cloudDiskCompressionHelper.getCompressionMode(any(), any(), any())).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_ON);
        when(cloudDiskCompressionHelper.getCompressionRatio(any(),any(),any())).thenReturn(2.0);


        Map<String, String> createParams = new HashMap<>();
        createParams.put("vpcid", "vpc-xxxmmxjqqi8go8153uq3b");
        createParams.put("vswitchid", "vsw-8vb5wxejg4owbtmkdepzo");
        createParams.put("connectionstring", "rm-2ze6ls1s41a33926zxx");
        createParams.put("dbinstancestoragetype","cloud_auto");
        createParams.put("PreferredBackupTime".toLowerCase(), "06:56Z");
        createParams.put("preferredbackupperiod".toLowerCase(), "1010101");
        createParams.put("sourcedbinstanceid","123");
        createParams.put("backupsetid","1234");
        CustInstanceDO sourceCustInstanceDO = new CustInstanceDO();
        sourceCustInstanceDO.setKindCode(18);
        sourceCustInstanceDO.setLevelId(544);
        when(custinsService.getCustInstanceByCustinsIdIgnoreDelete(any(),any(),any())).thenReturn(sourceCustInstanceDO);
        InstanceLevel level =new InstanceLevel();
        Map<String, Object> data = aliyunCreateDBInstanceService.createDBInstance(null, createParams);
        Assert.assertNotNull(data);
    }

    @Test
    public void testCreateDBInstance_rund_returnto_runc() throws Exception {
        when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        when(dependency.getDBaasMetaService()).thenReturn(dBaasMetaService);
        when(dependency.getResourceService()).thenReturn(resourceService);
        when(dependency.getPodParameterHelper()).thenReturn(podParameterHelper);
        when(dependency.getAvzSupport()).thenReturn(avzSupport);
        when(dependency.getRundPodSupport()).thenReturn(rundPodSupport);
        when(dependency.getCloudDiskCompressionHelper()).thenReturn(cloudDiskCompressionHelper);
        when(dependency.getReplicaSetService()).thenReturn(replicaSetService);
        when(dependency.getAligroupService()).thenReturn(aligroupService);
        when(dependency.getMySQLservice()).thenReturn(mySQLservice);
        GetBackupSetResponse backupSetResponse=new GetBackupSetResponse();
        backupSetResponse.setEngine("MySQL");
        backupSetResponse.setEngineVersion("5.7");
        backupSetResponse.setSlaveStatusObj(new GetBackupSetResponse.SlaveStatus());
        backupSetResponse.getSlaveStatusObj().setSnapshotId("snapshotId");
        BakhistoryDO bakhistoryDO=new BakhistoryDO();
        bakhistoryDO.setKindCode(18);
        AVZInfo avzInfo = new AVZInfo(ParamConstants.DispenseMode.ClassicDispenseMode, "regionId", "zoneId", "vswId", "regionId",null, new MultiAVZExParamDO());
        when(avzSupport.getAVZInfo(anyMap())).thenReturn(avzInfo);
        when(podParameterHelper.getAndCheckReplicaSetIpWhiteList()).thenReturn(new IPWhiteList());
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.BASIC);
        when(defaultApi.getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setId(1L);

        when(mysqlParamSupport.getParameterValue(any(),eq(ParamConstants.STORAGE),any())).thenReturn("100");
        when(mysqlParamSupport.getAndCheckDBType(any(), any())).thenReturn("MySQL");
//        when(mysqlParamSupport.getParameterValue(any(), eq(ParamConstants.SOURCE_DBINSTANCE_ID))).thenReturn("123");
        when(mysqlParamSupport.getAndCheckDBVersion(anyMap(),anyString(),anyBoolean())).thenReturn("5.7");
        when(podParameterHelper.getDiskType(any())).thenReturn("cloud_auto");
        when(podParameterHelper.getBizType(any(), any())).thenReturn(ReplicaSet.BizTypeEnum.ALIYUN);

        when(cloudDiskCompressionHelper.getCompressionMode(any(), any(), any())).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_ON);
        when(cloudDiskCompressionHelper.getCompressionRatio(any(),any(),any())).thenReturn(2.0);
        CustInstanceDO srcCustins =new CustInstanceDO();
        when(dependency.getPodCommonSupport()).thenReturn(podCommonSupport);
        when(minorVersionServiceHelper.getServiceSpecTag(any(), any(ReplicaSet.BizTypeEnum.class), any(), any(), any(), anyInt(), any(InstanceLevel.class), anyString(), anyBoolean(), anyBoolean(),null)).thenReturn("serviceSpecTag");

        Map<String, String> createParams = new HashMap<>();
        createParams.put("vpcid", "vpc-xxxmmxjqqi8go8153uq3b");
        createParams.put("vswitchid", "vsw-8vb5wxejg4owbtmkdepzo");
        createParams.put("connectionstring", "rm-2ze6ls1s41a33926zxx");
        createParams.put("dbinstancestoragetype","cloud_auto");
        createParams.put("PreferredBackupTime".toLowerCase(), "06:56Z");
        createParams.put("preferredbackupperiod".toLowerCase(), "1010101");
//        createParams.put("sourcedbinstanceid","123");
        createParams.put("backupsetid","1234");
        CustInstanceDO sourceCustInstanceDO = new CustInstanceDO();
        sourceCustInstanceDO.setKindCode(18);
        sourceCustInstanceDO.setLevelId(544);
        when(dependency.getRundPodSupport()).thenReturn(rundPodSupport);
        when(rundPodSupport.routeToRund(anyString(),anyBoolean())).thenReturn(true);
        when(rundPodSupport.getPodTypeByGrayConfig(any(),any(),any(),any())).thenReturn(PodType.POD_ECS_RUND);
        InstanceLevel level =new InstanceLevel();
        Map<String, Object> data = aliyunCreateDBInstanceService.createDBInstance(null, createParams);
        Assert.assertNotNull(data);
    }

    @Test
    public void testCreateDBInstance_xchg() throws Exception {
        when(dependency.getMysqlParamSupport()).thenReturn(mysqlParamSupport);
        when(dependency.getDBaasMetaService()).thenReturn(dBaasMetaService);
        when(dependency.getResourceService()).thenReturn(resourceService);
        when(dependency.getPodParameterHelper()).thenReturn(podParameterHelper);
        when(dependency.getAvzSupport()).thenReturn(avzSupport);
        when(dependency.getRundPodSupport()).thenReturn(rundPodSupport);
        when(dependency.getCloudDiskCompressionHelper()).thenReturn(cloudDiskCompressionHelper);
        when(dependency.getReplicaSetService()).thenReturn(replicaSetService);
        when(dependency.getAligroupService()).thenReturn(aligroupService);
        when(dependency.getMySQLservice()).thenReturn(mySQLservice);
        when(dependency.getPodCommonSupport()).thenReturn(podCommonSupport);
        when(dependency.getPodTemplateHelper()).thenReturn(podTemplateHelper);
        BakhistoryDO bakhistoryDO=new BakhistoryDO();
        bakhistoryDO.setKindCode(18);
        AVZInfo avzInfo = new AVZInfo(ParamConstants.DispenseMode.ClassicDispenseMode, "regionId", "zoneId", "vswId", "regionId",null, new MultiAVZExParamDO());
        when(avzSupport.getAVZInfo(anyMap())).thenReturn(avzInfo);
        when(podParameterHelper.getAndCheckReplicaSetIpWhiteList()).thenReturn(new IPWhiteList());
        when(dBaasMetaService.getDefaultClient()).thenReturn(defaultApi);
        InstanceLevel instanceLevel = new InstanceLevel();
        instanceLevel.setCategory(InstanceLevel.CategoryEnum.BASIC);
        instanceLevel.setExtraInfo("{\"instructionSetArch\":\"x86hg\"}");
        when(defaultApi.getInstanceLevel(any(), any(), any(), any(), any())).thenReturn(instanceLevel);
        ReplicaSet replicaSet = new ReplicaSet();
        replicaSet.setId(1L);

        when(mysqlParamSupport.getParameterValue(any(),eq(ParamConstants.STORAGE),any())).thenReturn("100");
        when(mysqlParamSupport.getAndCheckDBType(any(), any())).thenReturn("MySQL");
        when(mysqlParamSupport.getAndCheckDBVersion(anyMap(),anyString(),anyBoolean())).thenReturn("5.7");
        when(podParameterHelper.getDiskType(any())).thenReturn("cloud_auto");
        when(podParameterHelper.getBizType(any(), any())).thenReturn(ReplicaSet.BizTypeEnum.ALIYUN);

        when(cloudDiskCompressionHelper.getCompressionMode(any(), any(), any())).thenReturn(CloudDiskCompressionHelper.COMPRESSION_MODE_ON);
        when(cloudDiskCompressionHelper.getCompressionRatio(any(),any(),any())).thenReturn(2.0);
        when(minorVersionServiceHelper.getServiceSpecTag(
                any(),any(), any(), any(), any(), anyInt(), any(), any(), anyBoolean(), anyMap()))
                .thenReturn("alisql_xchg_docker_image_cloud_disk_20241231");
        when(rundPodSupport.getPodTypeByGrayConfig(any(),any(),any(),any())).thenReturn(PodType.POD_RUNC);
        ScheduleTemplate scheduleTemplate=new ScheduleTemplate();
        Pair<String, ScheduleTemplate> stringScheduleTemplatePair = new ImmutablePair<>("key", scheduleTemplate);
        when(podTemplateHelper.getBizSysScheduleTemplate(any(PodType.class),any(ReplicaSet.BizTypeEnum.class),anyString(),any(InstanceLevel.class),anyBoolean(),any(),any(),any(),any())).thenReturn(stringScheduleTemplatePair);


        Map<String, String> createParams = new HashMap<>();
        createParams.put("vpcid", "vpc-xxxmmxjqqi8go8153uq3b");
        createParams.put("vswitchid", "vsw-8vb5wxejg4owbtmkdepzo");
        createParams.put("connectionstring", "rm-2ze6ls1s41a33926zxx");
        createParams.put("dbinstancestoragetype","cloud_auto");
        createParams.put("PreferredBackupTime".toLowerCase(), "06:56Z");
        createParams.put("preferredbackupperiod".toLowerCase(), "1010101");
        CustInstanceDO sourceCustInstanceDO = new CustInstanceDO();
        sourceCustInstanceDO.setKindCode(18);
        sourceCustInstanceDO.setLevelId(544);
        InstanceLevel level =new InstanceLevel();
        Map<String, Object> data = aliyunCreateDBInstanceService.createDBInstance(null, createParams);
        Assert.assertNotNull(data);
    }


}